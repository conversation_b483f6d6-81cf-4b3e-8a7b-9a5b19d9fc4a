<?php
session_start([
    'cookie_secure'   => true,       // Only send cookie over HTTPS
    'cookie_httponly' => true,       // <PERSON><PERSON> cannot access the cookie
    'cookie_samesite' => 'Strict'    // Prevent CSRF in modern browsers
]);
include '../../../../connection/dbconnect.php';
if ($_SESSION['loginStatus'] != "isLogin") {
    header("Location: ../logout.php");
    exit;
} else {

    if ($_SERVER["REQUEST_METHOD"] == "POST") {
        if (isset($_POST['boatResponseBtn'])) {
            try {

                if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
                    throw new Exception("Invalid CSRF token.");
                }
                if (!isset($_SESSION['id'])) {
                    throw new Exception("User not authenticated");
                }

                // Validate Booking ID
                $getBookingId = $_POST['booking_id'] ?? null;
                if (!$getBookingId) {
                    throw new Exception("Invalid booking ID.");
                }

                $boatResponse = trim($_POST['boatResponse']);
                $referenceNumber = trim($_POST['referenceNumber']);

                if ($boatResponse === "Approved") {
                    header("Location: ../add-passenger.php?id=" . $getBookingId);
                    exit();
                } else {
                    // Start transaction
                    $pdo->beginTransaction();

                    $updateBookingStatus = "declined";

                    // Update booking status to declined
                    $stmt = $pdo->prepare("UPDATE cb_bookings SET booking_status = :booking_status WHERE booking_id = :booking_id");
                    $stmt->execute([
                        ':booking_status' => $updateBookingStatus,
                        ':booking_id' => $getBookingId
                    ]);

                    // Update boat approval status
                    $stmt = $pdo->prepare("UPDATE cb_booking_approvals SET boat = :boatResponse WHERE booking_id = :booking_id");
                    $stmt->execute([
                        ':boatResponse' => $boatResponse,
                        ':booking_id' => $getBookingId
                    ]);

                    // Get booking details to insert into cancellation table
                    $stmt = $pdo->prepare(
                        "SELECT * FROM cb_bookings WHERE booking_id = :booking_id"
                    );
                    $stmt->execute([':booking_id' => $getBookingId]);
                    $booking = $stmt->fetch(PDO::FETCH_ASSOC);

                    if (!$booking) {
                        throw new Exception('Booking not found.');
                    }

                    // Insert booking data into cb_booking_cancellation table with status 'declined'
                    $stmt = $pdo->prepare(
                        "INSERT INTO cb_booking_cancellation
                        (booking_id, referenceNum, tour_operator_id, resort_operator_id, boat_id, port_id,
                        check_in_date, check_out_date, booking_status, date_created)
                        VALUES
                        (:booking_id, :referenceNum, :tour_operator_id, :resort_operator_id, :boat_id, :port_id,
                        :check_in_date, :check_out_date, 'declined', NOW())"
                    );

                    $stmt->execute([
                        ':booking_id' => $booking['booking_id'],
                        ':referenceNum' => $booking['referenceNum'],
                        ':tour_operator_id' => $booking['tour_operator_id'],
                        ':resort_operator_id' => $booking['resort_operator_id'],
                        ':boat_id' => $booking['boat_id'],
                        ':port_id' => $booking['port_id'],
                        ':check_in_date' => $booking['check_in_date'],
                        ':check_out_date' => $booking['check_out_date']
                    ]);

                    // Insert log entry
                    $type = "Booking - " . $boatResponse;
                    $description = "Boat operator: " . $_SESSION['username'] . " Declined a booking: " . $referenceNumber  . ". Date Created: " . date("Y-m-d");
                    $stmt = $pdo->prepare("INSERT INTO system_logs (type, description) VALUES (:type, :description)");
                    $stmt->execute([
                        ':type' => $type,
                        ':description' => $description
                    ]);

                    // Commit transaction
                    $pdo->commit();

                    $_SESSION['success'] = $boatResponse . " successfully";
                    header("Location: ../transaction-approved.php");
                    exit();
                }
            } catch (Exception $e) {
                if ($pdo->inTransaction()) {
                    $pdo->rollBack();
                }

                // Enhanced error messages
                $errorMessage = $e->getMessage();
                if (strpos($errorMessage, 'CSRF') !== false) {
                    $_SESSION['error'] = 'Security token expired. Please refresh the page and try again.';
                } elseif (strpos($errorMessage, 'booking ID') !== false) {
                    $_SESSION['error'] = 'Invalid booking reference. Please contact support if this persists.';
                } elseif (strpos($errorMessage, 'authenticated') !== false) {
                    $_SESSION['error'] = 'Session expired. Please log in again.';
                } else {
                    $_SESSION['error'] = 'An unexpected error occurred: ' . $errorMessage;
                }

                header("Location: ../transaction-pending.php");
                exit();
            }
        }
    }
}
